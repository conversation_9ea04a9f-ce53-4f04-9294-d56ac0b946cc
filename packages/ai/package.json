{"name": "@onlook/ai", "description": "A AI library for Onlook", "main": "./src/index.ts", "type": "module", "module": "src/index.ts", "types": "src/index.ts", "version": "0.0.0", "private": true, "repository": {"type": "git", "url": "https://github.com/onlook-dev/onlook.git"}, "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "AI"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/typescript": "*"}, "dependencies": {"@ai-sdk/amazon-bedrock": "2.2.10", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/google-vertex": "2.2.24", "@ai-sdk/openai": "^1.3.22", "@mendable/firecrawl-js": "^1.29.1", "ai": "^4.3.10", "fg": "^0.0.3", "marked": "^15.0.7", "openai": "^4.103.0"}}