# ------------- Required Keys -------------

# Supabase - Enables our backend such as database and auth
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY="<Fill in from content after running supabase start>"

# Drizzle - Our ORM for accessing the database
SUPABASE_DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Anthropic - Enables AI chat. Other providers are optional below
ANTHROPIC_API_KEY="<Your api key from https://console.anthropic.com/settings/keys>"

# Codesandbox - Used to host user apps. Other providers may be supported in the future. May be optional in the future.
CSB_API_KEY="<Your api key from https://codesandbox.io/t/api>"

# Fast apply model providers to reliably resolve code changes. Either will work since we will fall back.
# Option 1: MorphLLM
MORPH_API_KEY="<Your api key from https://morphllm.com/dashboard>"
# Option 2: Relace
RELACE_API_KEY="<Your api key from https://app.relace.ai/settings/api-keys>"

# ------------- Optional Keys -------------

# PostHog - Enables analytics for the app
NEXT_PUBLIC_POSTHOG_KEY="<Your PostHog API key from https://posthog.com/docs/libraries/next-js>"
NEXT_PUBLIC_POSTHOG_HOST="<Your PostHog region https://us.i.posthog.com>"

# Resend - Enables inviting other users to the project using emails
RESEND_API_KEY="<Your api key from https://resend.com/api-keys>"

# Freestyle - Website hosting provider
FREESTYLE_API_KEY="<Your api key from https://admin.freestyle.sh/>"
NEXT_PUBLIC_HOSTING_DOMAIN="<Your hosting domain that you've set up with Freestyle>"

# Stripe - Pricing setup. You should not need this
STRIPE_WEBHOOK_SECRET="<Your Stripe webhook secret from https://dashboard.stripe.com/webhooks>" 
STRIPE_SECRET_KEY="<Your Stripe secret key from https://dashboard.stripe.com/apikeys>"

# Firecrawl - Scrape and screenshot websites
FIRECRAWL_API_KEY="<Your API Key from https://www.firecrawl.dev/app>"

# ------------- Optional: Alternative LLM providers -------------

# Google AI Studio
GOOGLE_AI_STUDIO_API_KEY="<Your Google API key from https://aistudio.google.com/apikey>"

# Open AI
OPENAI_API_KEY="<Your OpenAI API key from https://platform.openai.com/api-keys>"

# AWS Bedrock
AWS_ACCESS_KEY_ID="<Your AWS access key from https://ai-sdk.dev/providers/ai-sdk-providers/amazon-bedrock>"
AWS_SECRET_ACCESS_KEY="<Your AWS access key from https://ai-sdk.dev/providers/ai-sdk-providers/amazon-bedrock>"
AWS_REGION="<Your AWS region from https://ai-sdk.dev/providers/ai-sdk-providers/amazon-bedrock>"

# Google Vertex
GOOGLE_PROJECT_ID="<Your project ID from https://ai-sdk.dev/providers/ai-sdk-providers/google-vertex>"
GOOGLE_LOCATION="<Your Vertex model region from https://ai-sdk.dev/providers/ai-sdk-providers/google-vertex>"
GOOGLE_CLIENT_EMAIL="<Your client email from https://ai-sdk.dev/providers/ai-sdk-providers/google-vertex>"
GOOGLE_PRIVATE_KEY="<Your private key from https://ai-sdk.dev/providers/ai-sdk-providers/google-vertex>"
GOOGLE_PRIVATE_KEY_ID="<Your private key id from https://ai-sdk.dev/providers/ai-sdk-providers/google-vertex>"

# n8n - Automation webhooks
N8N_WEBHOOK_URL="<Your webhook url>"
N8N_API_KEY="<Your n8n api key>"